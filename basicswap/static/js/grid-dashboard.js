/**
 * Modern Exchange Interface for BasicSwap
 * Provides professional crypto exchange layout with real-time orderbook
 */

class ExchangeInterface {
    constructor() {
        this.isExchangeMode = false;
        this.currentMarket = 'BTC/PART';
        this.orderbook = { asks: [], bids: [] };
        this.recentTrades = [];
        this.marketStats = {};

        // All available trading pairs in BasicSwap
        this.availableMarkets = [
            'BTC/PART', 'XMR/PART', 'LTC/PART', 'BCH/PART',
            'FIRO/PART', 'DASH/PART', 'DCR/PART', 'PIVX/PART',
            'WOW/PART', 'NAV/PART', 'BTC/XMR', 'LTC/XMR',
            'BCH/XMR', 'FIRO/XMR', 'DASH/XMR', 'DCR/XMR',
            'PIVX/XMR', 'WOW/XMR', 'NAV/XMR', 'LTC/BTC',
            'BCH/BTC', 'FIRO/BTC', 'DASH/BTC', 'DCR/BTC',
            'PIVX/BTC', 'WOW/BTC', 'NAV/BTC'
        ];

        this.init();
    }

    init() {
        this.setupEventListeners();
        this.setupOrderbook();
        this.setupChartControls();
        this.updateMarketData();

        // Update data every 5 seconds
        setInterval(() => {
            this.updateMarketData();
        }, 5000);
    }

    setupEventListeners() {
        // Exchange layout toggle
        const toggleButton = document.getElementById('toggleLayout');
        if (toggleButton) {
            toggleButton.addEventListener('click', () => {
                this.toggleExchangeLayout();
            });
        }

        // Market selector
        const marketSelector = document.getElementById('market-selector');
        if (marketSelector) {
            // Populate with all available markets
            marketSelector.innerHTML = this.availableMarkets.map(market =>
                `<option value="${market}">${market}</option>`
            ).join('');

            marketSelector.addEventListener('change', (e) => {
                this.currentMarket = e.target.value;
                this.updateMarketData();
            });
        }
    }

    toggleExchangeLayout() {
        this.isExchangeMode = !this.isExchangeMode;

        const traditionalLayout = document.getElementById('traditional-layout');
        const exchangeLayout = document.getElementById('exchange-layout');
        const toggleButton = document.getElementById('toggleLayout');

        if (this.isExchangeMode) {
            traditionalLayout.classList.add('hidden');
            exchangeLayout.classList.remove('hidden');
            toggleButton.innerHTML = `
                <svg class="w-4 h-4 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
                </svg>
                Traditional View
            `;
            toggleButton.classList.remove('bg-blue-600', 'hover:bg-blue-700');
            toggleButton.classList.add('bg-gray-600', 'hover:bg-gray-700');

            // Initialize exchange layout
            this.initializeExchangeLayout();
        } else {
            traditionalLayout.classList.remove('hidden');
            exchangeLayout.classList.add('hidden');
            toggleButton.innerHTML = `
                <svg class="w-4 h-4 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 5a1 1 0 011-1h14a1 1 0 011 1v2a1 1 0 01-1 1H5a1 1 0 01-1-1V5zM4 13a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H5a1 1 0 01-1-1v-6zM16 13a1 1 0 011-1h2a1 1 0 011 1v6a1 1 0 01-1 1h-2a1 1 0 01-1-1v-6z"></path>
                </svg>
                Exchange View
            `;
            toggleButton.classList.remove('bg-gray-600', 'hover:bg-gray-700');
            toggleButton.classList.add('bg-blue-600', 'hover:bg-blue-700');
        }
    }

    initializeExchangeLayout() {
        this.renderOrderbook();
        this.renderRecentTrades();
        this.updateMarketStats();
    }

    setupChartControls() {
        // Chart resolution buttons
        const resolutionButtons = document.querySelectorAll('.resolution-button');
        resolutionButtons.forEach(button => {
            button.addEventListener('click', (e) => {
                // Remove active class from all buttons
                resolutionButtons.forEach(btn => btn.classList.remove('active'));
                // Add active class to clicked button
                e.target.classList.add('active');

                // Update chart (placeholder for now)
                console.log('Chart resolution changed to:', e.target.textContent);
            });
        });

        // Set default active resolution
        const defaultButton = document.getElementById('resolution-day');
        if (defaultButton) {
            defaultButton.classList.add('active');
        }
    }

    setupOrderbook() {
        // Listen for offers table updates to refresh orderbook
        this.observeOffersTable();
    }

    observeOffersTable() {
        const offersTable = document.getElementById('offers-body');
        if (!offersTable) return;

        // Create a MutationObserver to watch for changes in the offers table
        const observer = new MutationObserver((mutations) => {
            let shouldUpdate = false;
            mutations.forEach((mutation) => {
                if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
                    shouldUpdate = true;
                }
            });

            if (shouldUpdate) {
                // Debounce updates
                clearTimeout(this.orderbookUpdateTimeout);
                this.orderbookUpdateTimeout = setTimeout(() => {
                    this.updateMarketData();
                }, 500);
            }
        });

        observer.observe(offersTable, {
            childList: true,
            subtree: true
        });
    }

    updateMarketData() {
        // Extract real data from offers table
        this.extractOrderbookFromOffers();

        // Update all components if in exchange mode
        if (this.isExchangeMode) {
            this.renderOrderbook();
            this.renderRecentTrades();
            this.updateMarketStats();
        }
    }

    extractOrderbookFromOffers() {
        // Extract orderbook data from existing offers table
        const offersTable = document.getElementById('offers-body');
        if (!offersTable) {
            this.generateMockData();
            return;
        }

        const [baseCoin, quoteCoin] = this.currentMarket.split('/');
        const rows = offersTable.querySelectorAll('tr');
        const asks = [];
        const bids = [];
        const trades = [];

        rows.forEach(row => {
            const cells = row.querySelectorAll('td');
            if (cells.length < 7) return;

            try {
                // Extract coin information from the swap column (usually 5th column)
                const swapCell = cells[4];
                const swapText = swapCell.textContent.trim();

                // Check if this row matches our current market
                if (swapText.includes(baseCoin) && swapText.includes(quoteCoin)) {
                    const rateCell = cells[6]; // Rate column
                    const amountCell = cells[3]; // Amount column

                    if (rateCell && amountCell) {
                        const rateText = rateCell.textContent.replace(/[^\d.-]/g, '');
                        const amountText = amountCell.textContent.replace(/[^\d.-]/g, '');

                        const rate = parseFloat(rateText);
                        const amount = parseFloat(amountText);

                        if (!isNaN(rate) && !isNaN(amount) && rate > 0 && amount > 0) {
                            const order = {
                                price: rate.toFixed(8),
                                amount: amount.toFixed(6),
                                total: (rate * amount).toFixed(6),
                                timestamp: Date.now()
                            };

                            // Determine if it's a bid or ask based on the swap direction
                            const isSellingBase = swapText.indexOf(baseCoin) < swapText.indexOf(quoteCoin);

                            if (isSellingBase) {
                                asks.push(order);
                            } else {
                                bids.push(order);
                            }

                            // Add to recent trades (simulate)
                            trades.push({
                                price: rate.toFixed(8),
                                amount: (amount * 0.1).toFixed(6), // Simulate partial fill
                                side: isSellingBase ? 'sell' : 'buy',
                                time: new Date().toLocaleTimeString()
                            });
                        }
                    }
                }
            } catch (error) {
                console.warn('Error parsing offer row:', error);
            }
        });

        // Sort orders properly
        asks.sort((a, b) => parseFloat(a.price) - parseFloat(b.price));
        bids.sort((a, b) => parseFloat(b.price) - parseFloat(a.price));

        // Update orderbook data
        this.orderbook = {
            asks: asks.slice(0, 15), // Show top 15 orders
            bids: bids.slice(0, 15)
        };

        // Update recent trades
        this.recentTrades = trades.slice(-20); // Keep last 20 trades

        // Calculate market stats
        this.calculateMarketStats();
    }

    generateMockData() {
        // Generate realistic mock data when no real offers are available
        const [baseCoin, quoteCoin] = this.currentMarket.split('/');
        const basePrice = this.getBasePriceForPair(baseCoin, quoteCoin);

        const asks = [];
        const bids = [];
        const trades = [];

        // Generate asks (sell orders) - prices above market
        for (let i = 0; i < 15; i++) {
            const priceMultiplier = 1 + (i + 1) * 0.001; // 0.1% increments
            const price = basePrice * priceMultiplier;
            const amount = Math.random() * 10 + 0.1;

            asks.push({
                price: price.toFixed(8),
                amount: amount.toFixed(6),
                total: (price * amount).toFixed(6)
            });
        }

        // Generate bids (buy orders) - prices below market
        for (let i = 0; i < 15; i++) {
            const priceMultiplier = 1 - (i + 1) * 0.001; // 0.1% decrements
            const price = basePrice * priceMultiplier;
            const amount = Math.random() * 10 + 0.1;

            bids.push({
                price: price.toFixed(8),
                amount: amount.toFixed(6),
                total: (price * amount).toFixed(6)
            });
        }

        // Generate recent trades
        for (let i = 0; i < 20; i++) {
            const price = basePrice * (0.999 + Math.random() * 0.002);
            const amount = Math.random() * 5 + 0.1;
            const side = Math.random() > 0.5 ? 'buy' : 'sell';

            trades.push({
                price: price.toFixed(8),
                amount: amount.toFixed(6),
                side: side,
                time: new Date(Date.now() - i * 60000).toLocaleTimeString()
            });
        }

        this.orderbook = { asks, bids };
        this.recentTrades = trades;
        this.calculateMarketStats();
    }

    getBasePriceForPair(baseCoin, quoteCoin) {
        // Mock base prices for different pairs
        const prices = {
            'BTC': 45000, 'XMR': 150, 'LTC': 100, 'BCH': 300,
            'FIRO': 5, 'DASH': 80, 'DCR': 25, 'PIVX': 0.5,
            'WOW': 0.02, 'NAV': 0.3, 'PART': 1
        };

        const basePrice = prices[baseCoin] || 1;
        const quotePrice = prices[quoteCoin] || 1;

        return basePrice / quotePrice;
    }

    calculateMarketStats() {
        const { asks, bids } = this.orderbook;

        if (asks.length === 0 || bids.length === 0) {
            this.marketStats = {
                lastPrice: '0.00000000',
                change24h: '0.00',
                volume24h: '0.00',
                spread: '0.00'
            };
            return;
        }

        const bestAsk = parseFloat(asks[0].price);
        const bestBid = parseFloat(bids[0].price);
        const lastPrice = (bestAsk + bestBid) / 2;
        const spread = ((bestAsk - bestBid) / bestBid * 100);

        // Mock 24h change and volume
        const change24h = (Math.random() - 0.5) * 10; // -5% to +5%
        const volume24h = Math.random() * 1000 + 100;

        this.marketStats = {
            lastPrice: lastPrice.toFixed(8),
            change24h: change24h.toFixed(2),
            volume24h: volume24h.toFixed(2),
            spread: spread.toFixed(2)
        };
    }

    renderOrderbook() {
        const orderbookContainer = document.getElementById('modern-orderbook');
        if (!orderbookContainer) return;

        const { asks, bids } = this.orderbook;

        orderbookContainer.innerHTML = `
            <!-- Orderbook Header -->
            <div class="orderbook-header">
                <span>Price (${this.currentMarket.split('/')[1]})</span>
                <span>Amount (${this.currentMarket.split('/')[0]})</span>
                <span>Total</span>
            </div>

            <!-- Asks (Sell Orders) -->
            <div class="orderbook-section" id="asks-section">
                ${asks.map(order => `
                    <div class="orderbook-row ask" data-price="${order.price}">
                        <span>${order.price}</span>
                        <span>${order.amount}</span>
                        <span>${order.total}</span>
                    </div>
                `).join('')}
            </div>

            <!-- Spread -->
            <div class="orderbook-spread">
                ${this.marketStats.spread}% Spread
            </div>

            <!-- Bids (Buy Orders) -->
            <div class="orderbook-section" id="bids-section">
                ${bids.map(order => `
                    <div class="orderbook-row bid" data-price="${order.price}">
                        <span>${order.price}</span>
                        <span>${order.amount}</span>
                        <span>${order.total}</span>
                    </div>
                `).join('')}
            </div>
        `;

        // Add click handlers for order rows
        this.addOrderbookClickHandlers();
    }

    addOrderbookClickHandlers() {
        const orderRows = document.querySelectorAll('.orderbook-row');
        orderRows.forEach(row => {
            row.addEventListener('click', () => {
                const price = row.dataset.price;
                console.log('Selected price:', price);
                // Here you could populate a trading form with the selected price
            });
        });
    }

    renderRecentTrades() {
        const tradesContainer = document.getElementById('recent-trades');
        if (!tradesContainer) return;

        tradesContainer.innerHTML = `
            <div class="orderbook-header">
                <span>Price</span>
                <span>Amount</span>
                <span>Time</span>
            </div>
            ${this.recentTrades.map(trade => `
                <div class="trade-row trade-${trade.side}">
                    <span>${trade.price}</span>
                    <span>${trade.amount}</span>
                    <span>${trade.time}</span>
                </div>
            `).join('')}
        `;
    }

    updateMarketStats() {
        // Update market stats in the right panel
        const elements = {
            'last-price': this.marketStats.lastPrice,
            'price-change': this.marketStats.change24h + '%',
            'volume-24h': this.marketStats.volume24h,
            'spread': this.marketStats.spread + '%'
        };

        Object.entries(elements).forEach(([id, value]) => {
            const element = document.getElementById(id);
            if (element) {
                element.textContent = value;

                // Add color coding for price change
                if (id === 'price-change') {
                    const change = parseFloat(this.marketStats.change24h);
                    element.className = change >= 0 ?
                        'font-semibold text-green-600 dark:text-green-400' :
                        'font-semibold text-red-600 dark:text-red-400';
                }
            }
        });

        // Update exchange volume in top bar
        const exchangeVolume = document.getElementById('exchange-volume');
        if (exchangeVolume) {
            exchangeVolume.textContent = this.marketStats.volume24h + ' ' + this.currentMarket.split('/')[1];
        }

        // Update buy/sell order summaries
        this.updateOrderSummaries();
    }

    updateOrderSummaries() {
        const buyOrdersContainer = document.getElementById('buy-orders-summary');
        const sellOrdersContainer = document.getElementById('sell-orders-summary');

        if (buyOrdersContainer && this.orderbook.bids.length > 0) {
            const topBids = this.orderbook.bids.slice(0, 3);
            buyOrdersContainer.innerHTML = topBids.map(order => `
                <div class="flex justify-between text-green-600 dark:text-green-400">
                    <span>${order.price}</span>
                    <span>${order.amount}</span>
                </div>
            `).join('');
        }

        if (sellOrdersContainer && this.orderbook.asks.length > 0) {
            const topAsks = this.orderbook.asks.slice(0, 3);
            sellOrdersContainer.innerHTML = topAsks.map(order => `
                <div class="flex justify-between text-red-600 dark:text-red-400">
                    <span>${order.price}</span>
                    <span>${order.amount}</span>
                </div>
            `).join('');
        }
    }

    // Utility functions
    formatNumber(num, decimals = 2) {
        return parseFloat(num).toFixed(decimals);
    }

    formatCurrency(num, currency = 'USD') {
        return new Intl.NumberFormat('en-US', {
            style: 'currency',
            currency: currency,
            minimumFractionDigits: 2,
            maximumFractionDigits: 8
        }).format(num);
    }

    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }

    showToast(message, type = 'info') {
        // Simple toast notification
        const toast = document.createElement('div');
        toast.className = `fixed top-4 right-4 z-50 p-4 rounded-lg shadow-lg text-white transition-all duration-300 ${
            type === 'success' ? 'bg-green-500' :
            type === 'error' ? 'bg-red-500' : 'bg-blue-500'
        }`;
        toast.textContent = message;

        document.body.appendChild(toast);

        // Animate in
        setTimeout(() => {
            toast.style.transform = 'translateX(0)';
            toast.style.opacity = '1';
        }, 10);

        // Animate out and remove
        setTimeout(() => {
            toast.style.transform = 'translateX(100%)';
            toast.style.opacity = '0';
            setTimeout(() => toast.remove(), 300);
        }, 3000);
    }

    // Handle responsive behavior for exchange layout
    handleResize() {
        if (!this.isExchangeMode) return;

        const width = window.innerWidth;
        const exchangeLayout = document.getElementById('exchange-layout');

        if (width <= 1024) {
            // Stack panels vertically on smaller screens
            exchangeLayout.classList.add('mobile-layout');
        } else {
            exchangeLayout.classList.remove('mobile-layout');
        }
    }
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.exchangeInterface = new ExchangeInterface();

    // Handle window resize
    window.addEventListener('resize', () => {
        if (window.exchangeInterface) {
            window.exchangeInterface.handleResize();
        }
    });

    // Initial resize handling
    if (window.exchangeInterface) {
        window.exchangeInterface.handleResize();
    }
});
