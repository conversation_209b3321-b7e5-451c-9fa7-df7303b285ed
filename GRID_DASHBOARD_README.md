# Grid Dashboard for BasicSwap Offers Page

## Overview

The offers.html page has been enhanced with a grid-based dashboard system that allows users to move components around and includes a real-time orderbook feature.

## Features

### 🎯 **Draggable Grid Components**
- **Price Chart**: Interactive price chart with time resolution controls
- **Coin Prices**: Real-time cryptocurrency price cards
- **Orderbook**: Live order book showing buy/sell orders for trading pairs
- **Filters**: Advanced filtering controls for offers
- **Offers Table**: Main offers/bids table with pagination

### 🔧 **Grid Controls**
- **Grid Mode Toggle**: Switch between normal view and grid editing mode
- **Reset Layout**: Restore default component positions
- **Persistent Storage**: Layout preferences saved in browser localStorage

### 📱 **Responsive Design**
- **Desktop**: 12-column grid layout
- **Tablet**: 8-column grid layout  
- **Mobile**: 4-column grid layout with stacked components

## How to Use

### Basic Usage
1. **View Mode** (Default): Normal page functionality with all components visible
2. **Grid Mode**: Click "Grid Mode" button to enable component dragging
3. **Drag Components**: In grid mode, drag component headers to reposition
4. **Save Layout**: Positions are automatically saved when you stop dragging
5. **Reset**: Click "Reset" to restore default layout

### Orderbook Features
- **Pair Selection**: Choose trading pairs from dropdown (BTC/PART, XMR/PART, etc.)
- **Real-time Updates**: Automatically refreshes every 10 seconds
- **Live Integration**: Updates when offers table changes
- **Color Coding**: Green for buy orders, red for sell orders

### Grid Mode Controls
```
┌─────────────────┐
│ 🔲 Grid Mode    │  ← Click to enable/disable grid editing
│ 🔄 Reset        │  ← Click to reset layout to default
└─────────────────┘
```

## Technical Implementation

### Files Modified/Created
- `basicswap/templates/offers.html` - Enhanced with grid structure
- `basicswap/static/css/style.css` - Added grid and orderbook styles  
- `basicswap/static/js/grid-dashboard.js` - New grid functionality

### Grid Structure
```html
<div id="dashboard-container">
  <div id="dashboard-grid" class="dashboard-grid">
    <div class="grid-item" data-component="chart" data-grid-w="8" data-grid-h="4">
      <!-- Price Chart Component -->
    </div>
    <div class="grid-item" data-component="orderbook" data-grid-w="4" data-grid-h="6">
      <!-- Orderbook Component -->
    </div>
    <!-- More components... -->
  </div>
</div>
```

### CSS Grid System
- Uses CSS Grid with 12-column layout
- Components span multiple columns/rows using `data-grid-w` and `data-grid-h`
- Responsive breakpoints adjust grid columns automatically
- Smooth transitions and hover effects

### JavaScript Architecture
```javascript
class GridDashboard {
  // Core functionality
  toggleGridMode()     // Enable/disable drag mode
  setupDragAndDrop()   // Handle component dragging
  saveLayout()         // Persist layout to localStorage
  
  // Orderbook features  
  updateOrderbook()    // Refresh orderbook data
  extractOrderbookFromOffers() // Parse real offers data
  observeOffersTable() // Watch for table updates
}
```

## Integration with Existing System

### WebSocket Integration
- Listens for offers table updates via MutationObserver
- Automatically refreshes orderbook when new offers arrive
- Maintains real-time synchronization with backend data

### Backward Compatibility
- All existing functionality preserved
- Grid system is additive enhancement
- Falls back gracefully if JavaScript disabled
- Mobile-friendly responsive design

### Data Sources
- **Real Data**: Extracts orderbook from actual offers when available
- **Mock Data**: Generates sample data for demonstration/testing
- **Hybrid Approach**: Seamlessly switches between real and mock data

## Browser Support

- **Modern Browsers**: Full functionality (Chrome, Firefox, Safari, Edge)
- **CSS Grid**: Required for layout (supported in all modern browsers)
- **localStorage**: Required for layout persistence
- **MutationObserver**: Required for real-time updates

## Performance Considerations

- **Debounced Updates**: Orderbook updates are debounced to prevent excessive refreshing
- **Efficient DOM Manipulation**: Minimal DOM changes during drag operations
- **Memory Management**: Event listeners properly cleaned up
- **Responsive Images**: Coin icons optimized for different screen sizes

## Future Enhancements

### Potential Additions
- **Component Resizing**: Allow users to resize grid components
- **More Trading Pairs**: Expand orderbook to support all available pairs
- **Advanced Charts**: Integration with TradingView or similar charting libraries
- **Layout Templates**: Predefined layouts for different user types
- **Export/Import**: Share layouts between users or devices

### API Integration
- **Real-time WebSocket**: Direct connection to trading data feeds
- **Historical Data**: Extended price history and volume data
- **Order Placement**: Direct trading functionality from orderbook
- **Portfolio View**: Personal holdings and P&L tracking

## Troubleshooting

### Common Issues
1. **Components not dragging**: Ensure Grid Mode is enabled
2. **Layout not saving**: Check browser localStorage permissions
3. **Orderbook not updating**: Verify offers table is loading properly
4. **Mobile layout issues**: Check responsive CSS media queries

### Debug Mode
Enable browser developer tools and check console for any JavaScript errors. The grid system logs important events for debugging.

---

**Note**: This implementation enhances the existing BasicSwap offers page without breaking any existing functionality. Users can continue to use the page normally or take advantage of the new grid features as desired.
